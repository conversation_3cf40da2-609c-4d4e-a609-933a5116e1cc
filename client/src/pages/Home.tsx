/**
 * CFO-Grade Home Dashboard
 *
 * Rebuilt home page with executive-level insights including:
 * - Real-time liquidity monitoring
 * - Fee savings vs legacy systems
 * - Exception alerts and workflow metrics
 * - Historical trends and projections
 */

import React from "react";
import { Link } from "wouter";
import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

// Dashboard Components
import { KpiCard, StablecoinLiquidityKpiCard, ExceptionKpiCard } from "@/components/dashboard/KpiCard";
import { TrendLine, LiquidityRunway } from "@/components/dashboard/TrendLine";
import { StackedBar, FeeSavingsChart } from "@/components/dashboard/StackedBar";
import { ProgressRing, StablecoinSettlement } from "@/components/dashboard/ProgressRing";
import { DSODPOCharts, generateDSOData, generateDPOData } from "@/components/dashboard/DSODPOCharts";
import { CounterpartyChart, generateCounterpartyData } from "@/components/dashboard/CounterpartyChart";

// Data Hooks
import { useCurrentMetrics } from "@/hooks/useCurrentMetrics";
import { useHistoricalSeries } from "@/hooks/useHistoricalSeries";

// Icons
import { TrendingUp, AlertTriangle, DollarSign, Clock } from "lucide-react";

const Home = () => {
  // Fetch current metrics and historical data
  const { metrics, isLoading: metricsLoading, loadingStates } = useCurrentMetrics();
  const { series, isLoading: seriesLoading } = useHistoricalSeries();

  // Generate demo data for mini charts
  const dsoData = generateDSOData();
  const dpoData = generateDPOData();
  const counterpartyData = generateCounterpartyData();

  // Navigation handlers for KPI cards
  const handleLiquidityClick = () => {
    // Navigate to liquidity detail page
    window.location.href = "/liquidity";
  };

  const handleAnalyticsClick = () => {
    // Navigate to analytics page
    window.location.href = "/analytics/fees";
  };

  const handleExceptionsClick = () => {
    // Navigate to accounts payable for pending approvals
    window.location.href = "/accounts-payable";
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <h1 className="text-4xl font-bold text-slate-900 mb-2">
              CFO Dashboard
            </h1>
            <p className="text-slate-600 text-lg">
              Real-time financial insights and payment operations overview
            </p>
          </motion.div>

          {/* Hero KPI Bar */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          >
            {/* Stablecoin Liquidity */}
            <div className="h-32">
              <StablecoinLiquidityKpiCard
                stablecoinBalances={metrics.stablecoinBalances}
                isLoading={loadingStates.stablecoinBalances}
                onClick={handleLiquidityClick}
              />
            </div>

            {/* Fee Savings vs Legacy */}
            <div className="h-32">
              <KpiCard
                title="Fee Savings vs Legacy"
                value={`${metrics.feeSavingsYTD}%`}
                subtitle="Year-to-date savings"
                trend={{
                  value: 2.3,
                  label: "vs last quarter",
                  isPositive: true
                }}
                icon={DollarSign}
                onClick={handleAnalyticsClick}
                aria-label={`Fee savings: ${metrics.feeSavingsYTD}% year-to-date`}
              />
            </div>

            {/* Payments Outflow Today */}
            <div className="h-32">
              <KpiCard
                title="Payments Outflow Today"
                value={metrics.paymentsToday}
                subtitle="Dollar outflow processed"
                trend={{
                  value: 12.5,
                  label: "vs yesterday",
                  isPositive: true
                }}
                icon={TrendingUp}
                isLoading={loadingStates.paymentsToday}
                aria-label={`$${metrics.paymentsToday.toLocaleString()} payments outflow today`}
              />
            </div>

            {/* Exception Alerts */}
            <div className="h-32">
              <ExceptionKpiCard
                agingApprovals={metrics.paymentsAwaitingApproval.length}
                openInvoices={metrics.openInvoices.length}
                isLoading={loadingStates.paymentsAwaitingApproval || loadingStates.openInvoices}
                onClick={handleExceptionsClick}
              />
            </div>
          </motion.div>

          {/* Row 1: Liquidity Runway and Fee Savings */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"
          >
            <LiquidityRunway
              data={series.liquidityRunway}
              isLoading={seriesLoading}
            />
            <FeeSavingsChart
              data={series.feeSavingsData}
              isLoading={seriesLoading}
            />
          </motion.div>

          {/* Row 2: Stablecoin Settlement and Top Counterparties */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"
          >
            <div className="h-80">
              <StablecoinSettlement
                value={metrics.stablecoinSettlementRate}
                isLoading={metricsLoading}
              />
            </div>
            <div className="h-80">
              <CounterpartyChart
                data={counterpartyData}
                isLoading={false}
              />
            </div>
          </motion.div>

          {/* Row 3: DSO and DPO Trends */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mb-8"
          >
            <div className="h-80">
              <DSODPOCharts
                dsoData={dsoData}
                dpoData={dpoData}
                isLoading={false}
              />
            </div>
          </motion.div>

          {/* Right Drawer - Persistent Tables */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-6"
          >
            {/* Aging Approvals */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Aging Approvals
                  <Badge variant="destructive">
                    {metrics.paymentsAwaitingApproval.length}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loadingStates.paymentsAwaitingApproval ? (
                  <div className="space-y-3">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex justify-between items-center">
                        <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                        <div className="h-4 w-16 bg-muted animate-pulse rounded" />
                      </div>
                    ))}
                  </div>
                ) : metrics.paymentsAwaitingApproval.length > 0 ? (
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {metrics.paymentsAwaitingApproval.slice(0, 5).map((payment: any) => (
                      <div key={payment.id} className="flex justify-between items-center p-2 hover:bg-muted/50 rounded">
                        <div>
                          <div className="font-medium text-sm">{payment.recipient}</div>
                          <div className="text-xs text-muted-foreground">{payment.reference}</div>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold">${payment.amount.toLocaleString()}</div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(payment.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    ))}
                    {metrics.paymentsAwaitingApproval.length > 5 && (
                      <Link href="/accounts-payable">
                        <div className="text-center text-sm text-blue-600 hover:text-blue-800 cursor-pointer pt-2">
                          View all {metrics.paymentsAwaitingApproval.length} aging approvals →
                        </div>
                      </Link>
                    )}
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground py-8">
                    No aging approvals
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Incoming Payments */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Incoming Payments
                  <Badge variant="secondary">
                    {metrics.incomingPayments.length}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loadingStates.incomingPayments ? (
                  <div className="space-y-3">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex justify-between items-center">
                        <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                        <div className="h-4 w-16 bg-muted animate-pulse rounded" />
                      </div>
                    ))}
                  </div>
                ) : metrics.incomingPayments.length > 0 ? (
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {metrics.incomingPayments.slice(0, 5).map((payment: any) => (
                      <div key={payment.id} className="flex justify-between items-center p-2 hover:bg-muted/50 rounded">
                        <div>
                          <div className="font-medium text-sm">{payment.sender}</div>
                          <div className="text-xs text-muted-foreground">{payment.reference}</div>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold">${payment.amount.toLocaleString()}</div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(payment.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    ))}
                    {metrics.incomingPayments.length > 5 && (
                      <Link href="/accounts-receivable">
                        <div className="text-center text-sm text-blue-600 hover:text-blue-800 cursor-pointer pt-2">
                          View all {metrics.incomingPayments.length} incoming payments →
                        </div>
                      </Link>
                    )}
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground py-8">
                    No incoming payments pending
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Footer */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="mt-12 text-center text-sm text-muted-foreground"
          >
            <p>
              Data refreshes automatically every minute.
              <Link href="/admin" className="text-blue-600 hover:text-blue-800 ml-1">
                Configure settings →
              </Link>
            </p>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Home;
