/**
 * Demo Data Generator for CFO Dashboard
 *
 * Generates deterministic historical time-series data for dashboard metrics
 * when no real data is available. Uses faker with fixed seed for reproducible
 * demo stories.
 */

// Note: We'll use a simple deterministic random generator instead of faker
// to avoid adding another dependency

interface DemoDataPoint {
  date: string;
  value: number;
}

interface BankBalance {
  accountId: string;
  label: string;
  currency: string;
  amount: number;
}

interface StablecoinBalance {
  wallet: string;
  symbol: string;
  amount: number;
}

/**
 * Simple deterministic random number generator using seed
 */
class SeededRandom {
  private seed: number;

  constructor(seed: number = 42) {
    this.seed = seed;
  }

  next(): number {
    this.seed = (this.seed * 9301 + 49297) % 233280;
    return this.seed / 233280;
  }

  nextInt(min: number, max: number): number {
    return Math.floor(this.next() * (max - min + 1)) + min;
  }

  nextFloat(min: number, max: number): number {
    return this.next() * (max - min) + min;
  }
}

/**
 * Generate historical time series data for a specific metric
 */
export function getDemoSeries(
  metric: "paymentsProcessed" | "feesSaved" | "fxSaved" | "timeSaved" | "touchlessRate",
  days: number = 730
): DemoDataPoint[] {
  const rng = new SeededRandom(42);
  const data: DemoDataPoint[] = [];
  const today = new Date();

  // Base values and growth rates for each metric
  const metricConfig = {
    paymentsProcessed: { base: 50, growth: 0.08, volatility: 0.15 },
    feesSaved: { base: 2500, growth: 0.12, volatility: 0.20 },
    fxSaved: { base: 1800, growth: 0.10, volatility: 0.18 },
    timeSaved: { base: 120, growth: 0.15, volatility: 0.12 },
    touchlessRate: { base: 0.65, growth: 0.08, volatility: 0.05 }
  };

  const config = metricConfig[metric];

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);

    // Calculate trend value with year-over-year growth
    const yearProgress = (days - i) / 365;
    const trendMultiplier = Math.pow(1 + config.growth, yearProgress);

    // Add some seasonal variation (higher in Q4, lower in summer)
    const month = date.getMonth();
    const seasonalFactor = 1 + 0.1 * Math.sin((month - 6) * Math.PI / 6);

    // Add daily volatility
    const volatilityFactor = 1 + (rng.nextFloat(-1, 1) * config.volatility);

    // Calculate final value
    let value = config.base * trendMultiplier * seasonalFactor * volatilityFactor;

    // Special handling for touchless rate (percentage)
    if (metric === "touchlessRate") {
      value = Math.min(0.95, Math.max(0.30, value)); // Cap between 30% and 95%
    }

    // Round appropriately
    if (metric === "touchlessRate") {
      value = Math.round(value * 1000) / 1000; // 3 decimal places
    } else {
      value = Math.round(value);
    }

    data.push({
      date: date.toISOString().split('T')[0],
      value
    });
  }

  return data;
}

/**
 * Generate demo bank balances
 */
export function getDemoBankBalances(): BankBalance[] {
  const rng = new SeededRandom(123);

  return [
    {
      accountId: "JPM-001",
      label: "JPMorgan Chase Operating",
      currency: "USD",
      amount: Math.round(rng.nextFloat(400000, 900000))
    },
    {
      accountId: "WF-002",
      label: "Wells Fargo Treasury",
      currency: "USD",
      amount: Math.round(rng.nextFloat(200000, 500000))
    },
    {
      accountId: "HSBC-003",
      label: "HSBC International",
      currency: "EUR",
      amount: Math.round(rng.nextFloat(150000, 400000))
    }
  ];
}

/**
 * Generate demo stablecoin balances
 */
export function getDemoStablecoinBalances(): StablecoinBalance[] {
  const rng = new SeededRandom(456);

  return [
    {
      wallet: "******************************************",
      symbol: "USDC",
      amount: Math.round(rng.nextFloat(120000, 350000))
    },
    {
      wallet: "0x8ba1f109551bD432803012645Hac136c22C85d",
      symbol: "USDT",
      amount: Math.round(rng.nextFloat(50000, 150000))
    }
  ];
}

/**
 * Generate demo payments count for today
 */
export function getDemoPaymentsToday(): number {
  const rng = new SeededRandom(789);
  return rng.nextInt(4, 18);
}

/**
 * Calculate YTD fee savings percentage vs legacy systems
 */
export function calculateYTDFeeSavings(): number {
  const feesSavedData = getDemoSeries("feesSaved", 365);
  const currentYear = new Date().getFullYear();

  // Filter for current year data
  const ytdData = feesSavedData.filter(point => {
    const pointYear = new Date(point.date).getFullYear();
    return pointYear === currentYear;
  });

  if (ytdData.length === 0) return 0;

  // Calculate average daily savings and extrapolate to percentage
  const avgDailySavings = ytdData.reduce((sum, point) => sum + point.value, 0) / ytdData.length;

  // Assume legacy system would cost 15-25% more
  const legacyCostMultiplier = 1.20; // 20% higher costs
  const savingsPercentage = ((legacyCostMultiplier - 1) / legacyCostMultiplier) * 100;

  return Math.round(savingsPercentage * 10) / 10; // Round to 1 decimal
}

/**
 * Get current month touchless rate
 */
export function getCurrentTouchlessRate(): number {
  const touchlessData = getDemoSeries("touchlessRate", 30);
  const recentData = touchlessData.slice(-7); // Last 7 days

  const avgRate = recentData.reduce((sum, point) => sum + point.value, 0) / recentData.length;
  return Math.round(avgRate * 100); // Convert to percentage
}

/**
 * Generate liquidity runway projection (30 days)
 */
export function getLiquidityRunwayProjection(): DemoDataPoint[] {
  const rng = new SeededRandom(999);
  const data: DemoDataPoint[] = [];
  const today = new Date();

  // Start with current total liquidity
  const bankBalances = getDemoBankBalances();
  const stablecoinBalances = getDemoStablecoinBalances();
  const totalLiquidity = bankBalances.reduce((sum, b) => sum + b.amount, 0) +
                        stablecoinBalances.reduce((sum, s) => sum + s.amount, 0);

  let currentLiquidity = totalLiquidity;

  for (let i = 0; i < 30; i++) {
    const date = new Date(today);
    date.setDate(date.getDate() + i);

    // Simulate daily cash flow (slightly negative trend with volatility)
    const dailyChange = rng.nextFloat(-15000, 5000); // Net outflow trend
    currentLiquidity += dailyChange;

    data.push({
      date: date.toISOString().split('T')[0],
      value: Math.round(currentLiquidity)
    });
  }

  return data;
}

/**
 * Generate time-to-settle trend (30 days rolling average)
 */
export function getTimeToSettleTrend(): DemoDataPoint[] {
  const rng = new SeededRandom(777);
  const data: DemoDataPoint[] = [];
  const today = new Date();

  for (let i = 29; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);

    // Simulate improving settlement times (trending down from ~3 days to ~1 day)
    const baseDays = 3.0 - (29 - i) * 0.05; // Gradual improvement
    const volatility = rng.nextFloat(-0.3, 0.3);
    const settleTime = Math.max(0.5, baseDays + volatility);

    data.push({
      date: date.toISOString().split('T')[0],
      value: Math.round(settleTime * 10) / 10 // Round to 1 decimal
    });
  }

  return data;
}

/**
 * Generate demo data for fee savings chart
 */
export function generateFeeSavingsData() {
  const months = [
    "Jan", "Feb", "Mar", "Apr", "May", "Jun",
    "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
  ];

  return months.map((month, index) => {
    // Simulate growing savings over the year
    const baseFeeSavings = 2000 + (index * 200) + Math.random() * 500;
    const baseFxSavings = 1500 + (index * 150) + Math.random() * 400;

    return {
      month,
      feesSaved: Math.round(baseFeeSavings),
      fxSaved: Math.round(baseFxSavings)
    };
  });
}