/**
 * Historical Series Hook for CFO Dashboard
 *
 * Fetches historical time-series data with fallback to demo data generation
 * when no real historical data exists in the system.
 */

import { useQuery } from "@tanstack/react-query";
import {
  getDemoSeries,
  getLiquidityRunwayProjection,
  getTimeToSettleTrend,
  generateFeeSavingsData
} from "@/lib/demoData";

interface DataPoint {
  date: string;
  value: number;
}

interface FeeSavingsData {
  month: string;
  feesSaved: number;
  fxSaved: number;
}

/**
 * Fetch historical series for a specific metric
 */
function useMetricSeries(
  metric: "paymentsProcessed" | "feesSaved" | "fxSaved" | "timeSaved" | "touchlessRate",
  days: number = 730
) {
  return useQuery<DataPoint[]>({
    queryKey: ["/api/v1/analytics/series", metric, days],
    queryFn: async () => {
      try {
        // Try to fetch from a hypothetical analytics endpoint
        const response = await fetch(`/api/v1/analytics/series/${metric}?days=${days}`, {
          credentials: "include",
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch ${metric} series`);
        }

        const data = await response.json();

        // If no real data or empty array, use demo data
        if (!data || data.length === 0) {
          return getDemoSeries(metric, days);
        }

        return data;
      } catch (error) {
        console.warn(`Using demo data for ${metric}:`, error);
        return getDemoSeries(metric, days);
      }
    },
    staleTime: 300000, // 5 minutes
    refetchInterval: 600000, // Refetch every 10 minutes
  });
}

/**
 * Fetch liquidity runway projection with payment and invoice data
 */
function useLiquidityRunway() {
  return useQuery<DataPoint[]>({
    queryKey: ["/api/v1/analytics/liquidity-runway", "approved-payments", "open-invoices"],
    queryFn: async () => {
      try {
        // Always fetch approved payments and open invoices for runway calculation
        const [paymentsResponse, invoicesResponse] = await Promise.all([
          fetch("/api/v1/payments/approved", { credentials: "include" }),
          fetch("/api/v1/invoices/open", { credentials: "include" })
        ]);

        const approvedPayments = paymentsResponse.ok ? await paymentsResponse.json() : [];
        const openInvoices = invoicesResponse.ok ? await invoicesResponse.json() : [];

        console.log('Fetched data for liquidity runway:', {
          approvedPayments: approvedPayments.length,
          openInvoices: openInvoices.length
        });

        // Always use the calculation with real data
        return getLiquidityRunwayProjection(approvedPayments, openInvoices);
      } catch (error) {
        console.warn("Error fetching liquidity runway data:", error);
        return getLiquidityRunwayProjection();
      }
    },
    staleTime: 30000, // 30 seconds - shorter to pick up changes faster
    refetchInterval: 60000, // Refetch every minute
  });
}

/**
 * Fetch time-to-settle trend
 */
function useTimeToSettleTrend() {
  return useQuery<DataPoint[]>({
    queryKey: ["/api/v1/analytics/time-to-settle"],
    queryFn: async () => {
      try {
        const response = await fetch("/api/v1/analytics/time-to-settle", {
          credentials: "include",
        });

        if (!response.ok) {
          throw new Error("Failed to fetch time-to-settle trend");
        }

        const data = await response.json();

        if (!data || data.length === 0) {
          return getTimeToSettleTrend();
        }

        return data;
      } catch (error) {
        console.warn("Using demo time-to-settle data:", error);
        return getTimeToSettleTrend();
      }
    },
    staleTime: 300000, // 5 minutes
    refetchInterval: 600000, // Refetch every 10 minutes
  });
}

/**
 * Fetch fee and FX savings data
 */
function useFeeSavingsData() {
  return useQuery<FeeSavingsData[]>({
    queryKey: ["/api/v1/analytics/fee-savings"],
    queryFn: async () => {
      try {
        const response = await fetch("/api/v1/analytics/fee-savings", {
          credentials: "include",
        });

        if (!response.ok) {
          throw new Error("Failed to fetch fee savings data");
        }

        const data = await response.json();

        if (!data || data.length === 0) {
          return generateFeeSavingsData();
        }

        return data;
      } catch (error) {
        console.warn("Using demo fee savings data:", error);
        return generateFeeSavingsData();
      }
    },
    staleTime: 300000, // 5 minutes
    refetchInterval: 600000, // Refetch every 10 minutes
  });
}

/**
 * Main hook for all historical series data
 */
export function useHistoricalSeries() {
  const { data: paymentsProcessed = [], isLoading: paymentsLoading } = useMetricSeries("paymentsProcessed");
  const { data: feesSaved = [], isLoading: feesLoading } = useMetricSeries("feesSaved");
  const { data: fxSaved = [], isLoading: fxLoading } = useMetricSeries("fxSaved");
  const { data: timeSaved = [], isLoading: timeLoading } = useMetricSeries("timeSaved");
  const { data: touchlessRate = [], isLoading: touchlessLoading } = useMetricSeries("touchlessRate");

  const { data: liquidityRunway = [], isLoading: liquidityLoading } = useLiquidityRunway();
  const { data: timeToSettle = [], isLoading: settleLoading } = useTimeToSettleTrend();
  const { data: feeSavingsData = [], isLoading: feeSavingsLoading } = useFeeSavingsData();

  const isLoading = paymentsLoading || feesLoading || fxLoading || timeLoading ||
                   touchlessLoading || liquidityLoading || settleLoading || feeSavingsLoading;

  return {
    series: {
      paymentsProcessed,
      feesSaved,
      fxSaved,
      timeSaved,
      touchlessRate,
      liquidityRunway,
      timeToSettle,
      feeSavingsData
    },
    isLoading,
    loadingStates: {
      paymentsProcessed: paymentsLoading,
      feesSaved: feesLoading,
      fxSaved: fxLoading,
      timeSaved: timeLoading,
      touchlessRate: touchlessLoading,
      liquidityRunway: liquidityLoading,
      timeToSettle: settleLoading,
      feeSavingsData: feeSavingsLoading
    }
  };
}

/**
 * Hook for specific metric series
 */
export function useMetricSeriesData(
  metric: "paymentsProcessed" | "feesSaved" | "fxSaved" | "timeSaved" | "touchlessRate",
  days?: number
) {
  return useMetricSeries(metric, days);
}

/**
 * Hook for liquidity-specific data
 */
export function useLiquidityData() {
  const { data: liquidityRunway = [], isLoading: liquidityLoading } = useLiquidityRunway();
  const { data: timeToSettle = [], isLoading: settleLoading } = useTimeToSettleTrend();

  return {
    liquidityRunway,
    timeToSettle,
    isLoading: liquidityLoading || settleLoading,
    loadingStates: {
      liquidityRunway: liquidityLoading,
      timeToSettle: settleLoading
    }
  };
}

/**
 * Hook for savings-specific data
 */
export function useSavingsData() {
  const { data: feeSavingsData = [], isLoading: feeSavingsLoading } = useFeeSavingsData();
  const { data: feesSaved = [], isLoading: feesLoading } = useMetricSeries("feesSaved");
  const { data: fxSaved = [], isLoading: fxLoading } = useMetricSeries("fxSaved");

  return {
    feeSavingsData,
    feesSaved,
    fxSaved,
    isLoading: feeSavingsLoading || feesLoading || fxLoading,
    loadingStates: {
      feeSavingsData: feeSavingsLoading,
      feesSaved: feesLoading,
      fxSaved: fxLoading
    }
  };
}
