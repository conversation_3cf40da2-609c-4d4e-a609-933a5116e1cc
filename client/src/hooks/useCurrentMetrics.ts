/**
 * Current Metrics Hook for CFO Dashboard
 *
 * Fetches real-time data from live endpoints with SWR caching
 * and fallback to demo data when no real data exists.
 */

import { useQuery } from "@tanstack/react-query";
import {
  getDemoBankBalances,
  getDemoStablecoinBalances,
  getDemoPaymentsToday,
  calculateYTDFeeSavings,
  getCurrentStablecoinSettlementRate
} from "@/lib/demoData";

interface BankBalance {
  accountId: string;
  label: string;
  currency: string;
  amount: number;
}

interface StablecoinBalance {
  wallet: string;
  symbol: string;
  amount: number;
}

interface PaymentData {
  count: number;
  payments: any[];
}

interface CurrentMetrics {
  bankBalances: BankBalance[];
  stablecoinBalances: StablecoinBalance[];
  paymentsToday: number;
  paymentsAwaitingApproval: any[];
  openInvoices: any[];
  incomingPayments: any[];
  feeSavingsYTD: number;
  stablecoinSettlementRate: number;
}

/**
 * Fetch bank balances with fallback to demo data
 */
function useBankBalances() {
  return useQuery<BankBalance[]>({
    queryKey: ["/api/v1/liquidity/banks"],
    queryFn: async () => {
      try {
        const response = await fetch("/api/v1/liquidity/banks", {
          credentials: "include",
        });
        if (!response.ok) {
          throw new Error("Failed to fetch bank balances");
        }
        const data = await response.json();

        // If no real data or empty array, use demo data
        if (!data || data.length === 0) {
          return getDemoBankBalances();
        }
        return data;
      } catch (error) {
        console.warn("Using demo bank balances:", error);
        return getDemoBankBalances();
      }
    },
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // Refetch every minute
  });
}

/**
 * Fetch stablecoin balances with fallback to demo data
 */
function useStablecoinBalances() {
  return useQuery<StablecoinBalance[]>({
    queryKey: ["/api/v1/liquidity/stablecoins"],
    queryFn: async () => {
      try {
        const response = await fetch("/api/v1/liquidity/stablecoins", {
          credentials: "include",
        });
        if (!response.ok) {
          throw new Error("Failed to fetch stablecoin balances");
        }
        const data = await response.json();

        // If no real data or empty array, use demo data
        if (!data || data.length === 0) {
          return getDemoStablecoinBalances();
        }
        return data;
      } catch (error) {
        console.warn("Using demo stablecoin balances:", error);
        return getDemoStablecoinBalances();
      }
    },
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // Refetch every minute
  });
}

/**
 * Fetch today's payments outflow in dollars
 */
function usePaymentsToday() {
  return useQuery<number>({
    queryKey: ["/api/v1/payments", { from: "today" }],
    queryFn: async () => {
      try {
        const response = await fetch("/api/v1/payments?from=today", {
          credentials: "include",
        });
        if (!response.ok) {
          throw new Error("Failed to fetch today's payments");
        }
        const data: PaymentData = await response.json();

        // Calculate total outflow amount instead of count
        const totalOutflow = data.payments?.reduce((sum, payment) => sum + (payment.amount || 0), 0) || 0;
        return totalOutflow;
      } catch (error) {
        console.warn("Using demo payments outflow:", error);
        // Return demo outflow amount instead of count
        const rng = new (class {
          private seed = 42;
          next() { this.seed = (this.seed * 9301 + 49297) % 233280; return this.seed / 233280; }
          nextFloat(min: number, max: number) { return this.next() * (max - min) + min; }
        })();
        return Math.round(rng.nextFloat(50000, 250000)); // $50K - $250K daily outflow
      }
    },
    staleTime: 60000, // 1 minute
    refetchInterval: 300000, // Refetch every 5 minutes
  });
}

/**
 * Fetch payments awaiting approval
 */
function usePaymentsAwaitingApproval() {
  return useQuery<any[]>({
    queryKey: ["/api/v1/payments/pending"],
    queryFn: async () => {
      const response = await fetch("/api/v1/payments/pending", {
        credentials: "include",
      });
      if (!response.ok) {
        throw new Error("Failed to fetch pending payments");
      }
      return response.json();
    },
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // Refetch every minute
  });
}

/**
 * Fetch open invoices
 */
function useOpenInvoices() {
  return useQuery<any[]>({
    queryKey: ["/api/v1/invoices/open"],
    queryFn: async () => {
      const response = await fetch("/api/v1/invoices/open", {
        credentials: "include",
      });
      if (!response.ok) {
        throw new Error("Failed to fetch open invoices");
      }
      return response.json();
    },
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // Refetch every minute
  });
}

/**
 * Fetch incoming payments
 */
function useIncomingPayments() {
  return useQuery<any[]>({
    queryKey: ["/api/v1/payments/incoming", { status: "pending" }],
    queryFn: async () => {
      const response = await fetch("/api/v1/payments/incoming?status=pending", {
        credentials: "include",
      });
      if (!response.ok) {
        throw new Error("Failed to fetch incoming payments");
      }
      return response.json();
    },
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // Refetch every minute
  });
}

/**
 * Main hook that combines all current metrics
 */
export function useCurrentMetrics() {
  const { data: bankBalances = [], isLoading: bankLoading, error: bankError } = useBankBalances();
  const { data: stablecoinBalances = [], isLoading: stablecoinLoading, error: stablecoinError } = useStablecoinBalances();
  const { data: paymentsToday = 0, isLoading: paymentsLoading, error: paymentsError } = usePaymentsToday();
  const { data: paymentsAwaitingApproval = [], isLoading: pendingLoading, error: pendingError } = usePaymentsAwaitingApproval();
  const { data: openInvoices = [], isLoading: invoicesLoading, error: invoicesError } = useOpenInvoices();
  const { data: incomingPayments = [], isLoading: incomingLoading, error: incomingError } = useIncomingPayments();

  // Calculate derived metrics
  const feeSavingsYTD = calculateYTDFeeSavings();
  const stablecoinSettlementRate = getCurrentStablecoinSettlementRate();

  const isLoading = bankLoading || stablecoinLoading || paymentsLoading ||
                   pendingLoading || invoicesLoading || incomingLoading;

  const hasError = bankError || stablecoinError || paymentsError ||
                  pendingError || invoicesError || incomingError;

  const metrics: CurrentMetrics = {
    bankBalances,
    stablecoinBalances,
    paymentsToday,
    paymentsAwaitingApproval,
    openInvoices,
    incomingPayments,
    feeSavingsYTD,
    stablecoinSettlementRate
  };

  return {
    metrics,
    isLoading,
    error: hasError,
    // Individual loading states for granular control
    loadingStates: {
      bankBalances: bankLoading,
      stablecoinBalances: stablecoinLoading,
      paymentsToday: paymentsLoading,
      paymentsAwaitingApproval: pendingLoading,
      openInvoices: invoicesLoading,
      incomingPayments: incomingLoading
    }
  };
}
