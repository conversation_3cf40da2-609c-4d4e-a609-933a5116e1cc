/**
 * Mini Chart Grid Component for CFO Dashboard
 * 
 * Compact grid of small charts for displaying multiple metrics
 * in a space-efficient layout with DSO/DPO trends and counterparty data.
 */

import React from "react";
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  ResponsiveContainer,
  Toolt<PERSON>
} from "recharts";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface MiniChartData {
  name: string;
  value: number;
}

interface MiniChartProps {
  title: string;
  data: MiniChartData[];
  type: "line" | "bar";
  color?: string;
  height?: number;
  formatValue?: (value: number) => string;
  isLoading?: boolean;
}

function MiniChart({
  title,
  data,
  type,
  color = "#8884d8",
  height = 80,
  formatValue = (val) => val.toString(),
  isLoading = false
}: MiniChartProps) {
  if (isLoading) {
    return (
      <Card className="h-full">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">{title}</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div 
            className="w-full bg-muted animate-pulse rounded"
            style={{ height }}
          />
        </CardContent>
      </Card>
    );
  }

  const Chart = type === "line" ? LineChart : BarChart;
  const ChartElement = type === "line" ? Line : Bar;

  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm">{title}</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div style={{ height }}>
          <ResponsiveContainer width="100%" height="100%">
            <Chart data={data} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
              <Tooltip
                formatter={(value: number) => [formatValue(value), title]}
                labelStyle={{ fontSize: '12px' }}
                contentStyle={{
                  backgroundColor: 'hsl(var(--background))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '4px',
                  fontSize: '11px'
                }}
              />
              {type === "line" ? (
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke={color}
                  strokeWidth={2}
                  dot={false}
                />
              ) : (
                <Bar dataKey="value" fill={color} />
              )}
            </Chart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}

interface MiniChartGridProps {
  dsoData: MiniChartData[];
  dpoData: MiniChartData[];
  counterpartyData: Array<{ name: string; volume: number; count: number }>;
  isLoading?: boolean;
  className?: string;
}

export function MiniChartGrid({
  dsoData = [],
  dpoData = [],
  counterpartyData = [],
  isLoading = false,
  className
}: MiniChartGridProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.4 }}
      className={cn("grid grid-cols-1 md:grid-cols-3 gap-4", className)}
    >
      {/* DSO Trend */}
      <MiniChart
        title="DSO Trend"
        data={dsoData}
        type="line"
        color="#ef4444"
        formatValue={(val) => `${val} days`}
        isLoading={isLoading}
      />

      {/* DPO Trend */}
      <MiniChart
        title="DPO Trend"
        data={dpoData}
        type="line"
        color="#3b82f6"
        formatValue={(val) => `${val} days`}
        isLoading={isLoading}
      />

      {/* Top Counterparties */}
      <CounterpartyChart
        data={counterpartyData}
        isLoading={isLoading}
      />
    </motion.div>
  );
}

/**
 * Specialized counterparty chart component
 */
interface CounterpartyChartProps {
  data: Array<{ name: string; volume: number; count: number }>;
  isLoading?: boolean;
}

function CounterpartyChart({ data, isLoading }: CounterpartyChartProps) {
  if (isLoading) {
    return (
      <Card className="h-full">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">Top Counterparties</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex justify-between items-center">
                <div className="h-3 w-20 bg-muted animate-pulse rounded" />
                <div className="h-3 w-12 bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const topCounterparties = data
    .sort((a, b) => b.volume - a.volume)
    .slice(0, 5);

  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm">Top Counterparties</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          {topCounterparties.map((counterparty, index) => (
            <div key={counterparty.name} className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  index === 0 ? "bg-green-500" :
                  index === 1 ? "bg-blue-500" :
                  index === 2 ? "bg-yellow-500" :
                  "bg-gray-400"
                )} />
                <span className="text-xs font-medium truncate max-w-[80px]">
                  {counterparty.name}
                </span>
              </div>
              <div className="text-right">
                <div className="text-xs font-semibold">
                  ${(counterparty.volume / 1000).toFixed(0)}K
                </div>
                <div className="text-xs text-muted-foreground">
                  {counterparty.count} txns
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Generate demo data for DSO trend
 */
export function generateDSOData(): MiniChartData[] {
  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"];
  return months.map((month, index) => ({
    name: month,
    value: Math.round(45 - (index * 2) + Math.random() * 3) // Improving trend
  }));
}

/**
 * Generate demo data for DPO trend
 */
export function generateDPOData(): MiniChartData[] {
  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"];
  return months.map((month, index) => ({
    name: month,
    value: Math.round(30 + (index * 1.5) + Math.random() * 2) // Slightly increasing
  }));
}

/**
 * Generate demo counterparty data
 */
export function generateCounterpartyData() {
  return [
    { name: "Boeing", volume: 450000, count: 12 },
    { name: "Kratos Defense", volume: 320000, count: 8 },
    { name: "Mod Pizza", volume: 180000, count: 24 },
    { name: "Bumble Bee Foods", volume: 150000, count: 18 },
    { name: "Pacific Logistics", volume: 95000, count: 6 },
    { name: "Global Shipping", volume: 75000, count: 15 },
    { name: "Tech Solutions", volume: 60000, count: 9 }
  ];
}
