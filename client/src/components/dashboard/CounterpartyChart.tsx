/**
 * Top Counterparties Chart Component
 * 
 * Displays the top counterparties by volume and transaction count
 * with made-up company names for demo purposes.
 */

import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface CounterpartyData {
  name: string;
  volume: number;
  count: number;
}

interface CounterpartyChartProps {
  data: CounterpartyData[];
  isLoading?: boolean;
  className?: string;
}

export function CounterpartyChart({ 
  data = [], 
  isLoading = false, 
  className 
}: CounterpartyChartProps) {
  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.3 }}
        className={className}
      >
        <Card className="h-full">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Top Counterparties</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-2">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="flex justify-between items-center">
                  <div className="h-3 w-20 bg-muted animate-pulse rounded" />
                  <div className="h-3 w-12 bg-muted animate-pulse rounded" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  const topCounterparties = data
    .sort((a, b) => b.volume - a.volume)
    .slice(0, 5);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.3 }}
      className={className}
    >
      <Card className="h-full">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">Top Counterparties</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            {topCounterparties.map((counterparty, index) => (
              <div key={counterparty.name} className="flex justify-between items-center">
                <div className="flex items-center space-x-2">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    index === 0 ? "bg-green-500" :
                    index === 1 ? "bg-blue-500" :
                    index === 2 ? "bg-yellow-500" :
                    "bg-gray-400"
                  )} />
                  <span className="text-xs font-medium truncate max-w-[100px]">
                    {counterparty.name}
                  </span>
                </div>
                <div className="text-right">
                  <div className="text-xs font-semibold">
                    ${(counterparty.volume / 1000).toFixed(0)}K
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {counterparty.count} txns
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

/**
 * Generate demo counterparty data with made-up company names
 */
export function generateCounterpartyData(): CounterpartyData[] {
  return [
    { name: "Apex Manufacturing", volume: 450000, count: 12 },
    { name: "Stellar Dynamics", volume: 320000, count: 8 },
    { name: "Quantum Logistics", volume: 180000, count: 24 },
    { name: "Nexus Industries", volume: 150000, count: 18 },
    { name: "Meridian Corp", volume: 95000, count: 6 },
    { name: "Zenith Solutions", volume: 75000, count: 15 },
    { name: "Pinnacle Systems", volume: 60000, count: 9 }
  ];
}
