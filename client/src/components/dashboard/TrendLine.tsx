/**
 * Trend Line Chart Component for CFO Dashboard
 * 
 * Reusable line chart component using Recharts for displaying
 * time-series data with responsive design and accessibility.
 */

import React from "react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine
} from "recharts";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { motion } from "framer-motion";

interface DataPoint {
  date: string;
  value: number;
  [key: string]: any;
}

interface TrendLineProps {
  title: string;
  description?: string;
  data: DataPoint[];
  dataKey?: string;
  xAxisKey?: string;
  color?: string;
  formatValue?: (value: number) => string;
  formatTooltip?: (value: number, label: string) => [string, string];
  showGrid?: boolean;
  showReferenceLine?: boolean;
  referenceValue?: number;
  height?: number;
  isLoading?: boolean;
  className?: string;
}

export function TrendLine({
  title,
  description,
  data = [],
  dataKey = "value",
  xAxisKey = "date",
  color = "#8884d8",
  formatValue,
  formatTooltip,
  showGrid = true,
  showReferenceLine = false,
  referenceValue,
  height = 300,
  isLoading = false,
  className
}: TrendLineProps) {
  const defaultFormatValue = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}K`;
    } else {
      return `$${value.toLocaleString()}`;
    }
  };

  const defaultFormatTooltip = (value: number, label: string): [string, string] => {
    const formattedValue = formatValue ? formatValue(value) : defaultFormatValue(value);
    const formattedDate = new Date(label).toLocaleDateString();
    return [formattedValue, formattedDate];
  };

  const tooltipFormatter = formatTooltip || defaultFormatTooltip;

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <div 
            className="w-full bg-muted animate-pulse rounded flex items-center justify-center"
            style={{ height }}
          >
            <span className="text-muted-foreground">Loading chart data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <div 
            className="w-full bg-muted/20 rounded flex items-center justify-center"
            style={{ height }}
          >
            <span className="text-muted-foreground">No data available</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3, delay: 0.1 }}
    >
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <div style={{ height }}>
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={data}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                {showGrid && (
                  <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                )}
                <XAxis 
                  dataKey={xAxisKey}
                  tickFormatter={(value) => {
                    const date = new Date(value);
                    return date.toLocaleDateString('en-US', { 
                      month: 'short', 
                      day: 'numeric' 
                    });
                  }}
                  className="text-xs"
                />
                <YAxis 
                  tickFormatter={formatValue || defaultFormatValue}
                  className="text-xs"
                />
                <Tooltip
                  formatter={tooltipFormatter}
                  labelFormatter={(label) => new Date(label).toLocaleDateString()}
                  contentStyle={{
                    backgroundColor: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px',
                    fontSize: '12px'
                  }}
                />
                {showReferenceLine && referenceValue && (
                  <ReferenceLine 
                    y={referenceValue} 
                    stroke="#ef4444" 
                    strokeDasharray="5 5"
                    label="Target"
                  />
                )}
                <Line
                  type="monotone"
                  dataKey={dataKey}
                  stroke={color}
                  strokeWidth={2}
                  dot={false}
                  activeDot={{ r: 4, stroke: color, strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

/**
 * Specialized component for liquidity runway projection
 */
interface LiquidityRunwayProps {
  data: DataPoint[];
  isLoading?: boolean;
}

export function LiquidityRunway({ data, isLoading }: LiquidityRunwayProps) {
  return (
    <TrendLine
      title="Liquidity Runway"
      description="30-day cash flow projection"
      data={data}
      color="#10b981"
      formatValue={(value) => `$${(value / 1000).toFixed(0)}K`}
      showReferenceLine={true}
      referenceValue={500000} // $500K minimum threshold
      isLoading={isLoading}
    />
  );
}

/**
 * Specialized component for time-to-settle trend
 */
interface TimeToSettleProps {
  data: DataPoint[];
  isLoading?: boolean;
}

export function TimeToSettle({ data, isLoading }: TimeToSettleProps) {
  return (
    <TrendLine
      title="Time-to-Settle"
      description="Rolling 30-day average settlement time"
      data={data}
      color="#f59e0b"
      formatValue={(value) => `${value.toFixed(1)} days`}
      formatTooltip={(value, label) => [
        `${value.toFixed(1)} days`,
        new Date(label).toLocaleDateString()
      ]}
      showReferenceLine={true}
      referenceValue={1.0} // 1 day target
      isLoading={isLoading}
    />
  );
}
