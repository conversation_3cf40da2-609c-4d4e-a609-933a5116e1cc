/**
 * Progress Ring Component for CFO Dashboard
 *
 * Circular progress indicator for displaying percentage-based metrics
 * like touchless reconciliation rates with smooth animations.
 */

import React from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface ProgressRingProps {
  title: string;
  description?: string;
  value: number; // Percentage (0-100)
  target?: number; // Target percentage
  size?: number; // Ring diameter
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  showTarget?: boolean;
  isLoading?: boolean;
  className?: string;
  formatValue?: (value: number) => string;
}

export function ProgressRing({
  title,
  description,
  value = 0,
  target = 90,
  size = 120,
  strokeWidth = 8,
  color = "#10b981",
  backgroundColor = "#e5e7eb",
  showTarget = true,
  isLoading = false,
  className,
  formatValue = (val) => `${val}%`
}: ProgressRingProps) {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = `${circumference} ${circumference}`;
  const strokeDashoffset = circumference - (value / 100) * circumference;

  // Target ring offset
  const targetOffset = circumference - (target / 100) * circumference;

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center" style={{ height: size + 40 }}>
            <div
              className="bg-muted animate-pulse rounded-full"
              style={{ width: size, height: size }}
            />
          </div>
        </CardContent>
      </Card>
    );
  }

  const isAboveTarget = value >= target;
  const progressColor = isAboveTarget ? color : "#f59e0b";

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3, delay: 0.3 }}
    >
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center space-y-4">
            {/* Progress Ring */}
            <div className="relative" style={{ width: size, height: size }}>
              <svg
                className="transform -rotate-90"
                width={size}
                height={size}
              >
                {/* Background circle */}
                <circle
                  cx={size / 2}
                  cy={size / 2}
                  r={radius}
                  stroke={backgroundColor}
                  strokeWidth={strokeWidth}
                  fill="transparent"
                />

                {/* Target ring (if enabled) */}
                {showTarget && (
                  <circle
                    cx={size / 2}
                    cy={size / 2}
                    r={radius}
                    stroke="#e5e7eb"
                    strokeWidth={2}
                    fill="transparent"
                    strokeDasharray={strokeDasharray}
                    strokeDashoffset={targetOffset}
                    strokeLinecap="round"
                    className="opacity-50"
                  />
                )}

                {/* Progress circle */}
                <motion.circle
                  cx={size / 2}
                  cy={size / 2}
                  r={radius}
                  stroke={progressColor}
                  strokeWidth={strokeWidth}
                  fill="transparent"
                  strokeDasharray={strokeDasharray}
                  strokeLinecap="round"
                  initial={{ strokeDashoffset: circumference }}
                  animate={{ strokeDashoffset }}
                  transition={{ duration: 1, ease: "easeInOut" }}
                />
              </svg>

              {/* Center text */}
              <div className="absolute inset-0 flex flex-col items-center justify-center">
                <motion.div
                  className="text-2xl font-bold"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                >
                  {formatValue(value)}
                </motion.div>
                {showTarget && (
                  <div className="text-xs text-muted-foreground">
                    Target: {formatValue(target)}
                  </div>
                )}
              </div>
            </div>

            {/* Status indicator */}
            <div className={cn(
              "flex items-center space-x-2 text-sm",
              isAboveTarget ? "text-green-600" : "text-amber-600"
            )}>
              <div className={cn(
                "w-2 h-2 rounded-full",
                isAboveTarget ? "bg-green-500" : "bg-amber-500"
              )} />
              <span>
                {isAboveTarget
                  ? `${(value - target).toFixed(1)}% above target`
                  : `${(target - value).toFixed(1)}% below target`
                }
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

/**
 * Specialized component for Stablecoin Settlement Rate
 */
interface StablecoinSettlementProps {
  value: number;
  isLoading?: boolean;
}

export function StablecoinSettlement({ value, isLoading }: StablecoinSettlementProps) {
  return (
    <ProgressRing
      title="Stablecoin Settlement"
      description="Month-to-date stablecoin vs total payments"
      value={value}
      target={75}
      color="#10b981"
      isLoading={isLoading}
    />
  );
}

/**
 * Mini progress ring for smaller displays
 */
interface MiniProgressRingProps {
  value: number;
  size?: number;
  color?: string;
  className?: string;
}

export function MiniProgressRing({
  value,
  size = 60,
  color = "#10b981",
  className
}: MiniProgressRingProps) {
  const strokeWidth = 4;
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = `${circumference} ${circumference}`;
  const strokeDashoffset = circumference - (value / 100) * circumference;

  return (
    <div className={cn("relative", className)} style={{ width: size, height: size }}>
      <svg
        className="transform -rotate-90"
        width={size}
        height={size}
      >
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="#e5e7eb"
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeLinecap="round"
          initial={{ strokeDashoffset: circumference }}
          animate={{ strokeDashoffset }}
          transition={{ duration: 1, ease: "easeInOut" }}
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <span className="text-xs font-medium">{value}%</span>
      </div>
    </div>
  );
}
