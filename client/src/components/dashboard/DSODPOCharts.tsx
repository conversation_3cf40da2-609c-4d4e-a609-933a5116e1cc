/**
 * DSO and DPO Trend Charts Component
 * 
 * Displays Days Sales Outstanding and Days Payable Outstanding trends
 * with unitized axes showing days.
 */

import React from "react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  ResponsiveContainer,
  Tooltip
} from "recharts";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface ChartData {
  name: string;
  value: number;
}

interface DSODPOChartsProps {
  dsoData: ChartData[];
  dpoData: ChartData[];
  isLoading?: boolean;
  className?: string;
}

function TrendChart({
  title,
  data,
  color,
  isLoading = false
}: {
  title: string;
  data: ChartData[];
  color: string;
  isLoading?: boolean;
}) {
  if (isLoading) {
    return (
      <Card className="h-full">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">{title}</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="w-full h-32 bg-muted animate-pulse rounded" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm">{title}</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div style={{ height: 120 }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
              <XAxis 
                dataKey="name" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 10 }}
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 10 }}
                tickFormatter={(value) => `${value}d`}
              />
              <Tooltip
                formatter={(value: number) => [`${value} days`, title]}
                labelStyle={{ fontSize: '12px' }}
                contentStyle={{
                  backgroundColor: 'hsl(var(--background))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '4px',
                  fontSize: '11px'
                }}
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke={color}
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 3, stroke: color, strokeWidth: 2 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}

export function DSODPOCharts({
  dsoData = [],
  dpoData = [],
  isLoading = false,
  className
}: DSODPOChartsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.4 }}
      className={cn("grid grid-cols-1 md:grid-cols-2 gap-6", className)}
    >
      {/* DSO Trend */}
      <TrendChart
        title="DSO Trend (Days)"
        data={dsoData}
        color="#ef4444"
        isLoading={isLoading}
      />

      {/* DPO Trend */}
      <TrendChart
        title="DPO Trend (Days)"
        data={dpoData}
        color="#3b82f6"
        isLoading={isLoading}
      />
    </motion.div>
  );
}

/**
 * Generate demo data for DSO trend
 */
export function generateDSOData(): ChartData[] {
  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"];
  return months.map((month, index) => ({
    name: month,
    value: Math.round(45 - (index * 2) + Math.random() * 3) // Improving trend
  }));
}

/**
 * Generate demo data for DPO trend
 */
export function generateDPOData(): ChartData[] {
  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"];
  return months.map((month, index) => ({
    name: month,
    value: Math.round(30 + (index * 1.5) + Math.random() * 2) // Slightly increasing
  }));
}
