import express, { type Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { z } from "zod";
import { insertPaymentSchema, insertInvoiceSchema, insertRemittanceSchema, insertReceivedPaymentSchema, fileFormats, invoices } from "@shared/schema";
import { processPaymentFile, processInvoiceFile, generateSamplePaymentFile, generateSampleInvoiceFile, detectFileFormat } from "./utils/fileProcessors";
import { generateRemittanceFile, generateSampleRemittanceFile } from "./utils/fileGenerators";
import multer from "multer";
import path from "path";
import { db } from "./db";
import { eq } from "drizzle-orm";

// Import demo mode components
import { isDemoMode } from "./middleware/demoMode";
import { demoEventBus } from "./services/demoEventBus";
import { demoPaymentGateway } from "./services/demoPaymentGateway";

// Import the network simulator conditionally based on environment flag
import { networkSimulator } from "./utils/networkSimulator";
// Create a dummy simulator for when the real one is disabled
const dummySimulator = {
  sendPayment: async (id: number, callback: () => Promise<void>) => {
    // Execute the callback immediately with no delay
    return callback();
  },
  receivePayment: async (id: number, callback: () => Promise<void>) => {
    // Execute the callback immediately with no delay
    return callback();
  }
};

// Use the real simulator only if explicitly enabled
const simulator = process.env.SIMULATOR_ENABLED === 'true' ? networkSimulator : dummySimulator;

// Configure multer for file uploads
const upload = multer({ storage: multer.memoryStorage() });

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);

  // Initialize demo mode WebSocket server
  demoEventBus.initialize(httpServer);

  // Demo mode is handled directly in the payment send route

  // Demo mode API endpoints
  app.get("/api/demo/status", (req: Request, res: Response) => {
    res.json({
      enabled: isDemoMode(),
      speed: parseInt(process.env.DEMO_SPEED || '3000'),
      clientCount: demoEventBus.getClientCount(),
      timestamp: new Date()
    });
  });

  app.post("/api/demo/clear-history", (req: Request, res: Response) => {
    if (isDemoMode()) {
      demoEventBus.clearHistory();
      res.json({ success: true, message: "Demo event history cleared" });
    } else {
      res.status(400).json({ error: "Demo mode not enabled" });
    }
  });

  app.get("/api/demo/simulator/status", (req: Request, res: Response) => {
    if (isDemoMode()) {
      res.json(demoIncomingPaymentSimulator.getQueueStatus());
    } else {
      res.status(400).json({ error: "Demo mode not enabled" });
    }
  });

  app.post("/api/demo/simulator/restart", async (req: Request, res: Response) => {
    if (isDemoMode()) {
      await demoIncomingPaymentSimulator.restart();
      res.json({ success: true, message: "Demo simulator restarted" });
    } else {
      res.status(400).json({ error: "Demo mode not enabled" });
    }
  });

  // Payment API
  app.get("/api/payments", async (req: Request, res: Response) => {
    try {
      const payments = await storage.getAllPayments();
      res.json(payments);
    } catch (error) {
      console.error("Error fetching payments:", error);
      res.status(500).json({ error: "Failed to fetch payments" });
    }
  });

  app.get("/api/payments/:id", async (req: Request, res: Response) => {
    try {
      const payment = await storage.getPayment(parseInt(req.params.id));
      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }
      res.json(payment);
    } catch (error) {
      console.error("Error fetching payment:", error);
      res.status(500).json({ error: "Failed to fetch payment" });
    }
  });

  app.post("/api/payments/:id/approve", async (req: Request, res: Response) => {
    try {
      // Check if request contains signature and message fields
      const { signature, message } = req.body;

      // Approve the payment and include signature if provided
      const payment = await storage.approvePayment(
        parseInt(req.params.id),
        signature,
        message
      );

      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }

      res.json(payment);
    } catch (error) {
      console.error("Error approving payment:", error);
      res.status(500).json({ error: "Failed to approve payment" });
    }
  });

  app.post("/api/payments/:id/revoke", async (req: Request, res: Response) => {
    try {
      const payment = await storage.revokePaymentApproval(parseInt(req.params.id));
      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }
      res.json(payment);
    } catch (error) {
      console.error("Error revoking payment approval:", error);
      res.status(500).json({ error: "Failed to revoke payment approval" });
    }
  });

  app.post("/api/payments/:id/send", async (req: Request, res: Response) => {
    try {
      // Check if demo mode is enabled and handle accordingly
      if (isDemoMode()) {
        console.log(`Demo mode: Handling payment send request for payment ${req.params.id}`);
        return demoPaymentGateway.handleSendPayment(req, res);
      }

      const paymentId = parseInt(req.params.id);
      const payment = await storage.getPayment(paymentId);

      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }

      if (!payment.approved) {
        return res.status(400).json({ error: "Payment must be approved before sending" });
      }

      // Start the network simulator and immediately return a pending status
      res.json({ status: "pending", message: "Payment is being processed" });

      // Process the payment with or without delay based on simulator setting
      await simulator.sendPayment(paymentId, async () => {
        try {
          // Update payment status to "Paid"
          const updatedPayment = await storage.sendPayment(paymentId);
          console.log("Updated payment to PAID:", updatedPayment);

          if (updatedPayment) {
            // Auto-loading of sent payments into received payments is now disabled
            // This allows for manual import of payments via the Import Receipt modal
            console.log(`Payment #${paymentId} marked as sent successfully`);

            // Note: The following code is commented out to prevent automatic creation of received payments
            // This was causing issues with the manual import flow
            /*
            const paymentData = {
              reference: updatedPayment.reference,
              amount: updatedPayment.amount,
              sender: updatedPayment.sender,
              recipient: "Your Company",
              invoice_id: null
            };

            // Automatically create corresponding received payment in AR
            const receivedPayment = await storage.createReceivedPayment(paymentData);

            // Auto-link to matching invoice if one exists
            const allInvoices = await storage.getAllInvoices();
            const matchingInvoice = allInvoices.find(invoice =>
              invoice.reference === receivedPayment.reference ||
              (invoice.reference.includes('-') && receivedPayment.reference.includes('-') &&
               invoice.reference.split('-').slice(1).join('-') === receivedPayment.reference.split('-').slice(1).join('-'))
            );

            if (matchingInvoice) {
              await storage.linkReceivedPaymentToInvoice(receivedPayment.id, matchingInvoice.id);
              await storage.updateInvoiceStatus(matchingInvoice.id, "Paid");
            }
            */
          }
        } catch (error) {
          console.error("Error in payment callback:", error);
        }
      });
    } catch (error) {
      console.error("Error sending payment:", error);
      // Error will be logged but not returned to client since response is already sent
    }
  });

  app.post("/api/payments/:id/generate-remittance", async (req: Request, res: Response) => {
    try {
      const formatSchema = z.enum(fileFormats.remittance);
      const parsedBody = z.object({ format: formatSchema }).parse(req.body);
      const format = parsedBody.format;

      const paymentId = parseInt(req.params.id);
      const payment = await storage.getPayment(paymentId);

      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }

      if (!payment.sent_at) {
        return res.status(400).json({ error: "Payment must be sent before generating remittance" });
      }

      // Generate remittance content
      const remittanceContent = generateRemittanceFile(payment, null, format);

      // Save the file
      const filePath = await storage.saveFile(remittanceContent, "remittance", format);

      // Create remittance record
      const remittance = await storage.createRemittance({
        payment_id: payment.id,
        format,
        file_path: filePath,
        amount: payment.amount,
        sender: payment.sender,
        recipient: "Your Company", // Add a default recipient value
        reference: payment.reference
      });

      // Update payment's remittance status
      await storage.updatePaymentRemittanceStatus(payment.id, remittance.id);

      res.json(remittance);
    } catch (error) {
      console.error("Error generating remittance:", error);
      res.status(500).json({ error: "Failed to generate remittance" });
    }
  });

  app.post("/api/upload_payment", upload.single("file"), async (req: Request, res: Response) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No file uploaded" });
      }

      // Convert buffer to string
      const fileContent = req.file.buffer.toString('utf8');

      // Auto-detect format and process file
      const paymentData = processPaymentFile(fileContent);
      console.log("Payment data parsed successfully:", paymentData.reference);

      // Check for duplicate reference number
      const existingPayment = await storage.getPaymentByReference(paymentData.reference);
      if (existingPayment) {
        return res.status(400).json({
          error: `Payment with reference number "${paymentData.reference}" already exists (ID: ${existingPayment.id})`
        });
      }

      // Create payment record
      const payment = await storage.createPayment(paymentData);
      console.log("Payment created successfully with ID:", payment.id);

      res.json(payment);
    } catch (error) {
      console.error("Error uploading payment file:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to upload payment file" });
    }
  });

  app.get("/api/sample_payment/:format", async (req: Request, res: Response) => {
    try {
      const formatSchema = z.enum(fileFormats.payment);
      const format = formatSchema.parse(req.params.format);

      // Generate sample file
      const sampleContent = generateSamplePaymentFile(format);

      // Set appropriate content type
      res.setHeader('Content-Type', 'text/plain');
      res.setHeader('Content-Disposition', `attachment; filename=sample_payment_${format}.txt`);
      res.send(sampleContent);
    } catch (error) {
      console.error("Error generating sample payment file:", error);
      res.status(500).json({ error: "Failed to generate sample payment file" });
    }
  });

  // Remittance API
  app.get("/api/remittances/:id", async (req: Request, res: Response) => {
    try {
      const remittance = await storage.getRemittance(parseInt(req.params.id));
      if (!remittance) {
        return res.status(404).json({ error: "Remittance not found" });
      }
      res.json(remittance);
    } catch (error) {
      console.error("Error fetching remittance:", error);
      res.status(500).json({ error: "Failed to fetch remittance" });
    }
  });

  app.get("/api/remittances/:id/download", async (req: Request, res: Response) => {
    try {
      const remittance = await storage.getRemittance(parseInt(req.params.id));
      if (!remittance) {
        return res.status(404).json({ error: "Remittance not found" });
      }

      // Get file content
      const fileContent = await storage.getFileContent(remittance.file_path);

      // Set appropriate headers for download
      res.setHeader('Content-Type', 'text/plain');
      res.setHeader('Content-Disposition', `attachment; filename=remittance_${remittance.id}_${remittance.format}.txt`);
      res.send(fileContent);
    } catch (error) {
      console.error("Error downloading remittance:", error);
      res.status(500).json({ error: "Failed to download remittance" });
    }
  });

  app.get("/api/remittances/:id/debug", async (req: Request, res: Response) => {
    try {
      const remittance = await storage.getRemittance(parseInt(req.params.id));
      if (!remittance) {
        return res.status(404).json({ error: "Remittance not found" });
      }

      // Get file content
      const fileContent = await storage.getFileContent(remittance.file_path);

      // Return content as text
      res.setHeader('Content-Type', 'text/plain');
      res.send(fileContent);
    } catch (error) {
      console.error("Error debugging remittance:", error);
      res.status(500).json({ error: "Failed to debug remittance" });
    }
  });

  // Invoice API
  app.get("/api/invoices", async (req: Request, res: Response) => {
    try {
      // Check and update invoice statuses based on due dates
      await updateOverdueInvoices();

      const status = req.query.status as string | undefined;
      const invoices = status
        ? await storage.getInvoicesByStatus(status)
        : await storage.getAllInvoices();

      res.json(invoices);
    } catch (error) {
      console.error("Error fetching invoices:", error);
      res.status(500).json({ error: "Failed to fetch invoices" });
    }
  });

  // Helper function to update invoice statuses based on due dates
  async function updateOverdueInvoices() {
    try {
      // Get all open invoices
      const openInvoices = await storage.getInvoicesByStatus("Open");
      const today = new Date();

      // Check each invoice if it's overdue
      for (const invoice of openInvoices) {
        const dueDate = new Date(invoice.due_date);

        // If due date has passed, update status to "Overdue"
        if (dueDate < today) {
          console.log(`Updating invoice ${invoice.id} status to Overdue (due date: ${dueDate.toDateString()})`);
          await storage.updateInvoiceStatus(invoice.id, "Overdue");
        }
      }
    } catch (error) {
      console.error("Error updating overdue invoices:", error);
    }
  }

  app.get("/api/invoices/:id", async (req: Request, res: Response) => {
    try {
      const invoice = await storage.getInvoice(parseInt(req.params.id));
      if (!invoice) {
        return res.status(404).json({ error: "Invoice not found" });
      }
      res.json(invoice);
    } catch (error) {
      console.error("Error fetching invoice:", error);
      res.status(500).json({ error: "Failed to fetch invoice" });
    }
  });

  app.post("/api/invoices", async (req: Request, res: Response) => {
    try {
      const invoiceData = insertInvoiceSchema.parse(req.body);
      const invoice = await storage.createInvoice(invoiceData);
      res.json(invoice);
    } catch (error) {
      console.error("Error creating invoice:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to create invoice" });
    }
  });

  app.post("/api/invoices/:id/update-status", async (req: Request, res: Response) => {
    try {
      const statusSchema = z.enum(["Open", "Overdue", "Paid", "Remitted"]);
      const { status } = z.object({ status: statusSchema }).parse(req.body);

      const invoice = await storage.updateInvoiceStatus(parseInt(req.params.id), status);
      if (!invoice) {
        return res.status(404).json({ error: "Invoice not found" });
      }

      res.json(invoice);
    } catch (error) {
      console.error("Error updating invoice status:", error);
      res.status(500).json({ error: "Failed to update invoice status" });
    }
  });

  app.post("/api/invoices/:id/generate-remittance", async (req: Request, res: Response) => {
    try {
      const formatSchema = z.enum(fileFormats.remittance);
      const parsedBody = z.object({ format: formatSchema }).parse(req.body);
      const format = parsedBody.format;

      const invoiceId = parseInt(req.params.id);
      const invoice = await storage.getInvoice(invoiceId);

      if (!invoice) {
        return res.status(404).json({ error: "Invoice not found" });
      }

      // Allow regeneration of remittance for both Paid and Remitted statuses
      if (!["Paid", "Remitted"].includes(invoice.status) || !invoice.payment_id) {
        return res.status(400).json({ error: "Invoice must be paid before generating remittance" });
      }

      // Get the associated payment
      const payment = await storage.getReceivedPayment(invoice.payment_id);
      if (!payment) {
        return res.status(400).json({ error: "Associated payment not found" });
      }

      // Generate remittance content
      const remittanceContent = generateRemittanceFile(payment, invoice, format);

      // Save the file
      const filePath = await storage.saveFile(remittanceContent, "invoice_remittance", format);

      // Create remittance record
      const remittance = await storage.createRemittance({
        payment_id: payment.id,
        format,
        file_path: filePath,
        amount: invoice.amount,
        sender: payment.sender,
        recipient: invoice.customer, // Use the invoice customer as recipient
        reference: invoice.reference
      });

      // Update payment's remittance status and invoice status
      await storage.updateReceivedPaymentRemittanceStatus(payment.id, remittance.id);

      // Also update the invoice's remittance info
      await storage.updateInvoiceStatus(invoiceId, "Remitted");

      // Update additional invoice fields for remittance
      const [updatedInvoice] = await db
        .update(invoices)
        .set({
          remittance_id: remittance.id,
          remittance_generated: true,
          remittance_generated_at: new Date()
        })
        .where(eq(invoices.id, invoiceId))
        .returning();

      res.json(remittance);
    } catch (error) {
      console.error("Error generating invoice remittance:", error);
      res.status(500).json({ error: "Failed to generate invoice remittance" });
    }
  });

  app.get("/api/invoices/:id/download-remittance", async (req: Request, res: Response) => {
    try {
      const invoice = await storage.getInvoice(parseInt(req.params.id));
      if (!invoice || !invoice.remittance_id) {
        return res.status(404).json({ error: "Invoice remittance not found" });
      }

      const remittance = await storage.getRemittance(invoice.remittance_id);
      if (!remittance) {
        return res.status(404).json({ error: "Remittance not found" });
      }

      // Get file content
      const fileContent = await storage.getFileContent(remittance.file_path);

      // Set appropriate headers for download
      res.setHeader('Content-Type', 'text/plain');
      res.setHeader('Content-Disposition', `attachment; filename=invoice_remittance_${invoice.id}_${remittance.format}.txt`);
      res.send(fileContent);
    } catch (error) {
      console.error("Error downloading invoice remittance:", error);
      res.status(500).json({ error: "Failed to download invoice remittance" });
    }
  });

  app.post("/api/upload_invoice", upload.single("file"), async (req: Request, res: Response) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No file uploaded" });
      }

      // Convert buffer to string
      const fileContent = req.file.buffer.toString('utf8');

      // Force the file format based on the content and route context
      // First, try to detect the format
      const detectedFormat = detectFileFormat(fileContent);
      console.log(`Detected format in upload_invoice: ${JSON.stringify(detectedFormat)}`);

      let invoiceData;

      // If it's detected as a payment file but we're in the invoice route,
      // try to process it as an invoice anyway since the context indicates it should be an invoice
      if (detectedFormat.type === 'payment') {
        console.log(`File detected as payment, but uploaded to invoice endpoint. Attempting to treat as invoice...`);

        // Try each invoice format
        try {
          if (fileContent.includes('ISA*') || fileContent.includes('GS*')) {
            console.log("Trying to process as EDI X12 invoice");
            invoiceData = processInvoiceFile(fileContent, 'EDI X12');
          } else if (fileContent.includes('<?xml')) {
            console.log("Trying to process as ISO20022 invoice");
            invoiceData = processInvoiceFile(fileContent, 'ISO20022');
          } else {
            // Create a mock ISO20022 structure from a PEXR2002-like format
            console.log("Converting PEXR2002-like format to ISO20022 for invoice processing");
            const lines = fileContent.trim().split('\n');

            // Extract potential invoice fields from PEXR2002-like format
            const reference = lines.find(l => l.startsWith('REF:'))?.substring(4).trim() ||
                           `INV-${new Date().toISOString().split('T')[0].replace(/-/g, '')}-${Math.floor(Math.random() * 1000)}`;
            const amountStr = lines.find(l => l.startsWith('AMT:'))?.substring(4).trim() || '1000.00';
            const amount = parseFloat(amountStr);
            const customer = lines.find(l => l.startsWith('SND:'))?.substring(4).trim() || 'Unknown Customer';

            // Create a simple invoice-like XML
            const mockXml = `<?xml version="1.0" encoding="UTF-8"?>
<Document>
  <CstmrInvcData>
    <Invc>
      <InvcId>${reference}</InvcId>
      <TtlInvcAmt>${amount}</TtlInvcAmt>
      <Dbtr><Nm>${customer}</Nm></Dbtr>
      <RmtInf><AddtlInf>Invoice generated from file upload</AddtlInf></RmtInf>
    </Invc>
  </CstmrInvcData>
</Document>`;

            invoiceData = processInvoiceFile(mockXml, 'ISO20022');
          }
        } catch (error) {
          console.error("Failed to convert payment format to invoice:", error);
          throw new Error(`Could not process file as invoice: ${error instanceof Error ? error.message : String(error)}`);
        }
      } else {
        // Normal processing for detected invoice files
        invoiceData = processInvoiceFile(fileContent);
      }

      console.log("Invoice data parsed successfully:", invoiceData.reference);

      // Check for duplicate reference number
      const existingInvoice = await storage.getInvoiceByReference(invoiceData.reference);
      if (existingInvoice) {
        return res.status(400).json({
          error: `Invoice with reference number "${invoiceData.reference}" already exists (ID: ${existingInvoice.id})`
        });
      }

      // Create invoice record
      const invoice = await storage.createInvoice(invoiceData);

      // Auto-link to any matching received payment if one exists
      // Look for any received payment with the same reference number
      const allReceivedPayments = await storage.getAllReceivedPayments();
      const matchingPayment = allReceivedPayments.find(payment =>
        // Exact match for reference numbers
        payment.reference === invoice.reference ||
        // Handle REF prefix variations but keep the unique part
        (payment.reference.includes('-') && invoice.reference.includes('-') &&
         payment.reference.split('-').slice(1).join('-') === invoice.reference.split('-').slice(1).join('-'))
      );

      if (matchingPayment && !matchingPayment.invoice_id) {
        console.log(`Auto-linking invoice ${invoice.id} to received payment ${matchingPayment.id}`);
        // Link the matching payment to the new invoice
        await storage.linkReceivedPaymentToInvoice(matchingPayment.id, invoice.id);

        // Update the invoice status to "Paid"
        await storage.updateInvoiceStatus(invoice.id, "Paid");

        // Return the updated invoice
        const updatedInvoice = await storage.getInvoice(invoice.id);
        res.json(updatedInvoice);
      } else {
        res.json(invoice);
      }
    } catch (error) {
      console.error("Error uploading invoice file:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to upload invoice file" });
    }
  });

  app.get("/api/sample_invoice/:format", async (req: Request, res: Response) => {
    try {
      const formatSchema = z.enum(fileFormats.invoice);
      const format = formatSchema.parse(req.params.format);

      // Generate sample file
      const sampleContent = generateSampleInvoiceFile(format);
      console.log(`Generated sample ${format} invoice content (first 100 chars): ${sampleContent.substring(0, 100)}`);

      // Validate the sample by testing our detection logic
      const detectedFormat = detectFileFormat(sampleContent);
      console.log(`Sample invoice detection result: ${JSON.stringify(detectedFormat)}`);

      if (detectedFormat.type !== 'invoice') {
        console.error(`Warning: Generated sample ${format} invoice was detected as ${detectedFormat.type}`);
      }

      // Set appropriate content type and filename extension based on format
      let filename = `sample_invoice_${format}.txt`;
      let contentType = 'text/plain';

      if (format === 'ISO20022') {
        filename = `sample_invoice_${format}.xml`;
        contentType = 'application/xml';
      }

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
      res.send(sampleContent);
    } catch (error) {
      console.error("Error generating sample invoice file:", error);
      res.status(500).json({ error: "Failed to generate sample invoice file" });
    }
  });

  // Sample Remittance Files API endpoint
  app.get("/api/sample_remittance/:format", async (req: Request, res: Response) => {
    try {
      const formatSchema = z.enum(fileFormats.remittance);
      const format = formatSchema.parse(req.params.format);

      // Generate sample remittance file
      const sampleContent = generateSampleRemittanceFile(format);

      // Set appropriate content type
      res.setHeader('Content-Type', 'text/plain');
      res.setHeader('Content-Disposition', `attachment; filename=sample_remittance_${format}.txt`);
      res.send(sampleContent);
    } catch (error) {
      console.error("Error generating sample remittance file:", error);
      res.status(500).json({ error: "Failed to generate sample remittance file" });
    }
  });

  // Received Payments API
  app.get("/api/received-payments", async (req: Request, res: Response) => {
    try {
      const receivedPayments = await storage.getAllReceivedPayments();
      res.json(receivedPayments);
    } catch (error) {
      console.error("Error fetching received payments:", error);
      res.status(500).json({ error: "Failed to fetch received payments" });
    }
  });

  app.get("/api/received-payments/:id", async (req: Request, res: Response) => {
    try {
      const receivedPayment = await storage.getReceivedPayment(parseInt(req.params.id));
      if (!receivedPayment) {
        return res.status(404).json({ error: "Received payment not found" });
      }
      res.json(receivedPayment);
    } catch (error) {
      console.error("Error fetching received payment:", error);
      res.status(500).json({ error: "Failed to fetch received payment" });
    }
  });

  app.post("/api/received-payments/:id/link-invoice", async (req: Request, res: Response) => {
    try {
      const { invoiceId } = z.object({ invoiceId: z.number() }).parse(req.body);

      const paymentId = parseInt(req.params.id);

      // First link the payment to the invoice
      const receivedPayment = await storage.linkReceivedPaymentToInvoice(paymentId, invoiceId);

      if (!receivedPayment) {
        return res.status(404).json({ error: "Received payment or invoice not found" });
      }

      // Ensure that we also update the invoice status to "Paid"
      // This is a crucial step to maintain data consistency
      const updatedInvoice = await storage.updateInvoiceStatus(invoiceId, "Paid");

      if (!updatedInvoice) {
        console.warn(`Failed to update invoice ${invoiceId} status to Paid after linking to payment ${paymentId}`);
      } else {
        console.log(`Successfully updated invoice ${invoiceId} status to Paid after linking to payment ${paymentId}`);
      }

      res.json({
        receivedPayment,
        invoice: updatedInvoice
      });
    } catch (error) {
      console.error("Error linking received payment to invoice:", error);
      res.status(500).json({ error: "Failed to link received payment to invoice" });
    }
  });

  app.post("/api/simulate_received_payment", async (req: Request, res: Response) => {
    try {
      // Only use fields that exist in the database
      const paymentData = {
        reference: req.body.reference,
        amount: req.body.amount,
        sender: req.body.sender,
        recipient: req.body.recipient || "Your Company",
        invoice_id: req.body.invoice_id || null
      };
      const receivedPayment = await storage.createReceivedPayment(paymentData);

      // Auto-linking to invoices has been disabled to allow for manual linking
      // This enables testing the full AR workflow with manual reconciliation
      console.log(`Created received payment ${receivedPayment.id} with reference ${receivedPayment.reference}`);

      res.json(receivedPayment);
    } catch (error) {
      console.error("Error creating received payment:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to create received payment" });
    }
  });

  /**
   * Import Received Payment (Accounts Receivable)
   *
   * This endpoint allows manual import of payment data for the Accounts Receivable workflow.
   * It accepts a JSON object with payment details and creates a received payment record.
   *
   * The endpoint handles both "sender" and "from" field names for flexibility,
   * accommodating both UI requirements and simulation formats.
   *
   * Request body format:
   * {
   *   "from": "Sender Company Name",  // or "sender"
   *   "amount": 1000.00,
   *   "reference": "REF-12345"
   * }
   */
  app.post('/api/import/received-payment', async (req: Request, res: Response) => {
    try {
      // Accept both "sender" (from frontend) and "from" (from network simulator) field names
      const { sender, from, amount, reference } = req.body;
      const senderName = sender || from; // Use sender if provided, otherwise use from

      if (!senderName) {
        return res.status(400).json({ error: "Sender name is required" });
      }

      const payment = await storage.markPaymentReceived({ from: senderName, amount, reference });
      if (!payment) return res.status(404).send('Reference not found');
      res.json(payment);
    } catch (error) {
      console.error("Error importing received payment:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to import received payment" });
    }
  });

  /**
   * Import Receipt (Accounts Payable)
   *
   * This endpoint allows manual import of receipt data for the Accounts Payable workflow.
   * It accepts a JSON object with receipt details and marks a payment as having its receipt imported.
   *
   * The endpoint maintains the payment's "Paid" status while making it appear in the reconciliation
   * column, enabling manual verification before generating reconciliation documents.
   *
   * Request body format:
   * {
   *   "account": "Recipient Account ID",
   *   "amount": 1000.00,
   *   "reference": "REF-12345"
   * }
   */
  app.post('/api/import/receipt', async (req: Request, res: Response) => {
    try {
      const { account, amount, reference } = req.body;
      const pay = await storage.markReceiptImported({ account, amount, reference });
      if (!pay) return res.status(404).send('Reference not found');
      res.json(pay);
    } catch (error) {
      console.error("Error importing receipt:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to import receipt" });
    }
  });

  // Admin API endpoints
  // Organization endpoints
  app.get("/api/admin/organization", async (req: Request, res: Response) => {
    try {
      // Return demo data by default
      const organization = {
        legalName: "Global Logistics Ltd.",
        displayName: "GlobalLogi",
        accountNumber: "GL-001",
        emailDomain: "globallogistics.com",
        industry: "Logistics & Supply Chain",
        fiscalCurrency: "USD",
        erpBaseUrl: "https://sap-gl-prod.example.com",
        erpApiKey: "demo-erp-api-key",
      };
      res.json(organization);
    } catch (error) {
      console.error("Error fetching organization:", error);
      res.status(500).json({ error: "Failed to fetch organization" });
    }
  });

  app.put("/api/admin/organization", async (req: Request, res: Response) => {
    try {
      // For now, just return the data back. In a real app, this would save to database
      const organizationData = req.body;
      res.json(organizationData);
    } catch (error) {
      console.error("Error updating organization:", error);
      res.status(500).json({ error: "Failed to update organization" });
    }
  });

  // Member endpoints
  app.get("/api/admin/members", async (req: Request, res: Response) => {
    try {
      // Return demo members data
      const members = [
        {
          id: "1",
          email: "<EMAIL>",
          firstName: "Maria",
          lastName: "Hughes",
          role: "Approver",
          tier: "Tier 1",
          filters: ["Boeing", "Mod Pizza"],
          status: "Active"
        },
        {
          id: "2",
          email: "<EMAIL>",
          firstName: "David",
          lastName: "Nguyen",
          role: "Approver",
          tier: "Tier 2",
          filters: ["Kratos Defense"],
          status: "Active"
        },
        {
          id: "3",
          email: "<EMAIL>",
          firstName: "Alex",
          lastName: "Choi",
          role: "Accounts Payable",
          filters: ["All"],
          status: "Active"
        },
        {
          id: "4",
          email: "<EMAIL>",
          firstName: "Sara",
          lastName: "Wilson",
          role: "Accounts Receivable",
          filters: ["Bumble Bee Foods"],
          status: "Pending"
        }
      ];
      res.json(members);
    } catch (error) {
      console.error("Error fetching members:", error);
      res.status(500).json({ error: "Failed to fetch members" });
    }
  });

  app.post("/api/admin/members", async (req: Request, res: Response) => {
    try {
      // For now, just return the data with a generated ID. In a real app, this would save to database
      const memberData = {
        id: Date.now().toString(),
        ...req.body,
        status: "Pending",
      };
      res.json(memberData);
    } catch (error) {
      console.error("Error creating member:", error);
      res.status(500).json({ error: "Failed to create member" });
    }
  });

  app.put("/api/admin/members/:id", async (req: Request, res: Response) => {
    try {
      // For now, just return the data. In a real app, this would update in database
      const memberData = {
        id: req.params.id,
        ...req.body,
      };
      res.json(memberData);
    } catch (error) {
      console.error("Error updating member:", error);
      res.status(500).json({ error: "Failed to update member" });
    }
  });

  app.patch("/api/admin/members/:id/disable", async (req: Request, res: Response) => {
    try {
      // For now, just return mock data. In a real app, this would update in database
      const memberData = {
        id: req.params.id,
        status: "Disabled",
      };
      res.json(memberData);
    } catch (error) {
      console.error("Error disabling member:", error);
      res.status(500).json({ error: "Failed to disable member" });
    }
  });

  app.post("/api/admin/members/:id/resend-invite", async (req: Request, res: Response) => {
    try {
      // For now, just return success. In a real app, this would send an email
      res.json({ message: "Invite resent successfully" });
    } catch (error) {
      console.error("Error resending invite:", error);
      res.status(500).json({ error: "Failed to resend invite" });
    }
  });

  // Approval predicate endpoints
  app.get("/api/admin/approval-predicate", async (req: Request, res: Response) => {
    try {
      // Return demo approval predicate
      const predicate = {
        tier1: "<EMAIL>",
        tier2: "<EMAIL>",
        accountOwner: "<EMAIL>",
      };
      res.json(predicate);
    } catch (error) {
      console.error("Error fetching approval predicate:", error);
      res.status(500).json({ error: "Failed to fetch approval predicate" });
    }
  });

  app.put("/api/admin/approval-predicate", async (req: Request, res: Response) => {
    try {
      // For now, just return the data back. In a real app, this would save to database
      const predicateData = req.body;
      res.json(predicateData);
    } catch (error) {
      console.error("Error updating approval predicate:", error);
      res.status(500).json({ error: "Failed to update approval predicate" });
    }
  });

  // Workflow Engine API endpoints
  app.post("/api/admin/workflow/import", async (req: Request, res: Response) => {
    try {
      // Handle file upload and parsing
      const { vendor, content, filename } = req.body;

      // Mock import result
      const importResult = {
        vendor,
        format: filename.split('.').pop(),
        workflows: [
          {
            name: `${vendor} Workflow`,
            description: `Imported from ${vendor}`,
            steps: [
              {
                name: "Manager Approval",
                sequence: 1,
                conditions: [],
                approvers: ["manager"],
                mode: "SERIAL"
              }
            ],
            metadata: { imported: true }
          }
        ],
        fieldMappings: {
          "amount": "amount",
          "currency": "currency",
          "supplier": "supplier.id"
        },
        warnings: [],
        errors: []
      };

      res.json(importResult);
    } catch (error) {
      console.error("Error importing workflow:", error);
      res.status(500).json({ error: "Failed to import workflow" });
    }
  });

  app.post("/api/admin/workflow/import/:vendor/pull", async (req: Request, res: Response) => {
    try {
      const { vendor } = req.params;

      // Mock API pull result
      const result = {
        success: true,
        workflowsImported: 3,
        lastSync: new Date().toISOString()
      };

      res.json(result);
    } catch (error) {
      console.error("Error pulling workflows:", error);
      res.status(500).json({ error: "Failed to pull workflows" });
    }
  });

  app.get("/api/admin/workflow/definitions", async (req: Request, res: Response) => {
    try {
      // Return mock workflow definitions
      const definitions = [
        {
          id: "wf-001",
          name: "Standard Invoice Approval",
          description: "Standard approval workflow for invoices",
          version: "1.0.0",
          documentType: "INVOICE",
          status: "ACTIVE",
          steps: [
            {
              id: "step-1",
              sequence: 1,
              name: "Manager Approval",
              predicate: {
                type: "comparison",
                field: "amount",
                operator: "lt",
                value: 10000
              },
              approverRoleIds: ["manager"],
              approvalMode: "SERIAL",
              timeoutHours: 24
            }
          ],
          createdAt: "2024-01-15T10:00:00Z",
          updatedAt: "2024-01-20T14:30:00Z",
          createdBy: "admin"
        }
      ];

      res.json(definitions);
    } catch (error) {
      console.error("Error fetching workflow definitions:", error);
      res.status(500).json({ error: "Failed to fetch workflow definitions" });
    }
  });

  app.put("/api/admin/workflow/definitions/:id", async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const workflowData = req.body;

      // Mock update
      const updatedWorkflow = {
        ...workflowData,
        id,
        updatedAt: new Date().toISOString()
      };

      res.json(updatedWorkflow);
    } catch (error) {
      console.error("Error updating workflow definition:", error);
      res.status(500).json({ error: "Failed to update workflow definition" });
    }
  });

  app.post("/api/engine/evaluate", async (req: Request, res: Response) => {
    try {
      const { workflowId, document } = req.body;

      // Mock evaluation result
      const result = {
        workflowId,
        documentId: document.id,
        approvers: [
          {
            stepId: "step-1",
            sequence: 1,
            approverUserIds: ["manager-1"],
            approvalMode: "SERIAL",
            status: "PENDING",
            requiredApprovals: 1,
            currentApprovals: 0
          }
        ],
        autoApproved: false,
        estimatedCompletionTime: 24,
        evaluatedAt: new Date().toISOString(),
        evaluationTimeMs: 2.5
      };

      res.json(result);
    } catch (error) {
      console.error("Error evaluating workflow:", error);
      res.status(500).json({ error: "Failed to evaluate workflow" });
    }
  });

  // User Import API endpoints
  app.post("/api/admin/users/import", async (req: Request, res: Response) => {
    try {
      // Handle file upload and parsing
      const { vendor, content, filename } = req.body;

      // Mock import result
      const importResult = {
        vendor,
        format: filename?.split('.').pop() || 'unknown',
        users: [
          {
            rawData: { imported: true },
            email: "<EMAIL>",
            firstName: "John",
            lastName: "Doe",
            erpUserId: "JDOE",
            erpRoles: ["F_BKPF_APP"],
            status: "ACTIVE"
          },
          {
            rawData: { imported: true },
            email: "<EMAIL>",
            firstName: "Jane",
            lastName: "Smith",
            erpUserId: "JSMITH",
            erpRoles: ["F_BKPF_BUK"],
            status: "ACTIVE"
          }
        ],
        fieldMappings: {
          "BNAME": "erpUserId",
          "SMTP_ADDR": "email",
          "NAME_FIRST": "firstName",
          "NAME_LAST": "lastName",
          "AGR_NAME": "erpRoleCodes"
        },
        warnings: [],
        errors: [],
        metadata: {
          fileName: filename,
          recordCount: 2,
          parsedAt: new Date().toISOString()
        }
      };

      res.json(importResult);
    } catch (error) {
      console.error("Error importing users:", error);
      res.status(500).json({ error: "Failed to import users" });
    }
  });

  app.post("/api/admin/users/import/:vendor/pull", async (req: Request, res: Response) => {
    try {
      const { vendor } = req.params;
      const credentials = req.body;

      // Mock API pull result
      const result = {
        vendor,
        format: "api",
        users: [
          {
            rawData: { apiPull: true },
            email: "<EMAIL>",
            firstName: "API",
            lastName: "User",
            erpUserId: "APIUSER",
            erpRoles: ["F_BKPF_APP"],
            status: "ACTIVE"
          }
        ],
        fieldMappings: {
          "user_id": "erpUserId",
          "email": "email",
          "first_name": "firstName",
          "last_name": "lastName",
          "roles": "erpRoleCodes"
        },
        warnings: [],
        errors: [],
        metadata: {
          apiEndpoint: credentials.baseUrl,
          recordCount: 1,
          pulledAt: new Date().toISOString()
        }
      };

      res.json(result);
    } catch (error) {
      console.error("Error pulling users:", error);
      res.status(500).json({ error: "Failed to pull users from API" });
    }
  });

  app.get("/api/admin/usersets", async (req: Request, res: Response) => {
    try {
      // Return mock user sets
      const userSets = [
        {
          id: "us-003",
          version: "1.2.0",
          name: "SAP User Import - December 2024",
          description: "Updated user roles and added new cost center assignments",
          vendor: "SAP",
          status: "ACTIVE",
          userCount: 47,
          changeLog: "Added 3 new users, updated 5 role assignments, removed 1 inactive user",
          createdAt: "2024-12-15T10:30:00Z",
          createdBy: "<EMAIL>",
          activatedAt: "2024-12-15T11:00:00Z"
        },
        {
          id: "us-002",
          version: "1.1.0",
          name: "Oracle User Sync - November 2024",
          description: "Quarterly user role synchronization from Oracle HCM",
          vendor: "Oracle",
          status: "ARCHIVED",
          userCount: 45,
          changeLog: "Updated 8 user roles, added 2 new approvers",
          createdAt: "2024-11-20T14:15:00Z",
          createdBy: "<EMAIL>",
          activatedAt: "2024-11-20T14:30:00Z"
        }
      ];

      res.json(userSets);
    } catch (error) {
      console.error("Error fetching user sets:", error);
      res.status(500).json({ error: "Failed to fetch user sets" });
    }
  });

  app.post("/api/admin/usersets/:id/rollback", async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Mock rollback operation
      const result = {
        success: true,
        userSetId: id,
        rolledBackAt: new Date().toISOString(),
        message: "User set rolled back successfully"
      };

      res.json(result);
    } catch (error) {
      console.error("Error rolling back user set:", error);
      res.status(500).json({ error: "Failed to rollback user set" });
    }
  });

  // CFO Dashboard API v1 endpoints
  app.get("/api/v1/liquidity/banks", async (req: Request, res: Response) => {
    try {
      // Return demo bank balances - in production this would connect to banking APIs
      const bankBalances = [
        {
          accountId: "JPM-001",
          label: "JPMorgan Chase Operating",
          currency: "USD",
          amount: 750000
        },
        {
          accountId: "WF-002",
          label: "Wells Fargo Treasury",
          currency: "USD",
          amount: 320000
        },
        {
          accountId: "HSBC-003",
          label: "HSBC International",
          currency: "EUR",
          amount: 280000
        }
      ];
      res.json(bankBalances);
    } catch (error) {
      console.error("Error fetching bank balances:", error);
      res.status(500).json({ error: "Failed to fetch bank balances" });
    }
  });

  app.get("/api/v1/liquidity/stablecoins", async (req: Request, res: Response) => {
    try {
      // Return demo stablecoin balances - in production this would query blockchain
      const stablecoinBalances = [
        {
          wallet: "******************************************",
          symbol: "USDC",
          amount: 245000
        },
        {
          wallet: "0x8ba1f109551bD432803012645Hac136c22C85d",
          symbol: "USDT",
          amount: 89000
        }
      ];
      res.json(stablecoinBalances);
    } catch (error) {
      console.error("Error fetching stablecoin balances:", error);
      res.status(500).json({ error: "Failed to fetch stablecoin balances" });
    }
  });

  app.get("/api/v1/payments", async (req: Request, res: Response) => {
    try {
      const { from } = req.query;

      if (from === "today") {
        // Count payments sent today
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const payments = await storage.getAllPayments();
        const todayPayments = payments.filter(p => {
          if (!p.sent_at) return false;
          const sentDate = new Date(p.sent_at);
          sentDate.setHours(0, 0, 0, 0);
          return sentDate.getTime() === today.getTime();
        });

        res.json({ count: todayPayments.length, payments: todayPayments });
      } else {
        // Return all payments
        const payments = await storage.getAllPayments();
        res.json(payments);
      }
    } catch (error) {
      console.error("Error fetching payments:", error);
      res.status(500).json({ error: "Failed to fetch payments" });
    }
  });

  app.get("/api/v1/payments/pending", async (req: Request, res: Response) => {
    try {
      const payments = await storage.getAllPayments();
      const pendingPayments = payments.filter(p => p.status === "Not Approved" && !p.approved);
      res.json(pendingPayments);
    } catch (error) {
      console.error("Error fetching pending payments:", error);
      res.status(500).json({ error: "Failed to fetch pending payments" });
    }
  });

  app.get("/api/v1/payments/approved", async (req: Request, res: Response) => {
    try {
      const payments = await storage.getAllPayments();
      const approvedPayments = payments.filter(p => p.approved && p.status !== "Sent");
      res.json(approvedPayments);
    } catch (error) {
      console.error("Error fetching approved payments:", error);
      res.status(500).json({ error: "Failed to fetch approved payments" });
    }
  });

  app.get("/api/v1/invoices/open", async (req: Request, res: Response) => {
    try {
      const invoices = await storage.getAllInvoices();
      const openInvoices = invoices.filter(i => i.status === "Open" || i.status === "Overdue");
      res.json(openInvoices);
    } catch (error) {
      console.error("Error fetching open invoices:", error);
      res.status(500).json({ error: "Failed to fetch open invoices" });
    }
  });

  app.get("/api/v1/payments/incoming", async (req: Request, res: Response) => {
    try {
      const { status } = req.query;
      const receivedPayments = await storage.getAllReceivedPayments();

      if (status === "pending") {
        const pendingPayments = receivedPayments.filter(p => p.status === "Unlinked");
        res.json(pendingPayments);
      } else {
        res.json(receivedPayments);
      }
    } catch (error) {
      console.error("Error fetching incoming payments:", error);
      res.status(500).json({ error: "Failed to fetch incoming payments" });
    }
  });

  app.post("/api/admin/users/mappings", async (req: Request, res: Response) => {
    try {
      const mappingData = req.body;

      // Mock save mapping
      const savedMapping = {
        id: `mapping-${Date.now()}`,
        ...mappingData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: "<EMAIL>"
      };

      res.json(savedMapping);
    } catch (error) {
      console.error("Error saving user mapping:", error);
      res.status(500).json({ error: "Failed to save user mapping" });
    }
  });

  app.get("/api/admin/users/sample/:vendor", async (req: Request, res: Response) => {
    try {
      const { vendor } = req.params;

      // Generate sample file content based on vendor
      let sampleContent = "";
      let contentType = "text/csv";
      let filename = `${vendor.toLowerCase()}_users_sample.csv`;

      switch (vendor) {
        case "SAP":
          sampleContent = `BNAME,SMTP_ADDR,NAME_FIRST,NAME_LAST,AGR_NAME,KOSTL,BUKRS,USTYP
MHUGHES,<EMAIL>,Maria,Hughes,F_BKPF_APP,CC-1001,GL01,0
DNGUYEN,<EMAIL>,David,Nguyen,F_BKPF_MGR,CC-2001,GL01,0`;
          break;
        case "Oracle":
          sampleContent = `user_id,email,first_name,last_name,roles,cost_center,legal_entity,status
MHUGHES,<EMAIL>,Maria,Hughes,AP_APPROVAL_LEVEL1,CC-1001,US-WEST,ACTIVE
DNGUYEN,<EMAIL>,David,Nguyen,AP_APPROVAL_LEVEL2,CC-2001,US-EAST,ACTIVE`;
          break;
        case "Dynamics365":
          sampleContent = `systemuserid,internalemailaddress,firstname,lastname,roles,businessunitid,organizationid,isdisabled
{guid1},<EMAIL>,Maria,Hughes,Purchasing manager,BU-001,ORG-001,false
{guid2},<EMAIL>,David,Nguyen,Finance manager,BU-002,ORG-001,false`;
          break;
        case "NetSuite":
          sampleContent = `id,email,firstname,lastname,roles,department,subsidiary,isinactive
123,<EMAIL>,Maria,Hughes,Approver,Finance,1,false
124,<EMAIL>,David,Nguyen,Senior Approver,Finance,1,false`;
          break;
        default:
          throw new Error(`Unsupported vendor: ${vendor}`);
      }

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.send(sampleContent);
    } catch (error) {
      console.error("Error generating sample file:", error);
      res.status(500).json({ error: "Failed to generate sample file" });
    }
  });

  // Start demo mode services if enabled
  if (isDemoMode()) {
    console.log('Demo mode enabled - demo payment gateway ready');

    // Note: Incoming payment simulator is disabled by default
    // Demo mode only simulates the blockchain/network parts of payments you send
    // Manual imports for invoices and payments remain available
  }

  return httpServer;
}
